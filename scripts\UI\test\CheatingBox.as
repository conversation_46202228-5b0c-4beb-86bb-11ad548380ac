package UI.test
{
   import UI.NormalUICtrl;
   import UI.base.NormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.test.CheatingDefine;
   import flash.display.Sprite;
   import flash.events.TextEvent;
   import flash.text.TextField;
   
   public class CheatingBox extends NormalUI
   {
      
      private var boxObj:Object = {};
      
      private var haveDataB:Boolean = false;
      
      private var boxTag:Sprite = null;
      
      private var menuTxt:TextField;
      
      private var nowLabel:String = "";
      
      private var nowBox:NormalBox;
      
      private var nowDefine:CheatingDefine;
      
      public function CheatingBox()
      {
         super();
      }
      
      public function initImg() : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("TestUI","cheatingBox");
         if(Boolean(img0)) {
            this.setImg(img0);
         } else {
            // 创建简单的作弊界面
            this.createSimpleCheatingUI();
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["boxTag","menuTxt"];
         super.setImg(img0);
         this.menuTxt.addEventListener(TextEvent.LINK,this.menuClick);
         this.menuTxt.styleSheet = ComMethod.getLinkCss("#CCCCCC","#00FFFF");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         if (!this.haveDataB) {
            this.createSimpleCheatingUI();
            this.haveDataB = true;
         }
      }
      


      private function createSimpleCheatingUI() : void
      {
         // 创建更大的作弊界面
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x333333, 0.9);
         bg.graphics.drawRect(0, 0, 800, 600);
         bg.graphics.endFill();
         addChild(bg);

         // 创建标题
         var titleTxt:TextField = new TextField();
         titleTxt.x = 10;
         titleTxt.y = 10;
         titleTxt.width = 700;
         titleTxt.height = 30;
         titleTxt.htmlText = "<font color='#00FF00' size='16'><b>作弊功能面板 - 增强版</b></font>";
         addChild(titleTxt);

         // 创建关闭按钮
         this.createCloseButton();

         // 创建功能按钮区域
         this.createCheatingButtons();
      }

      private function createCloseButton() : void
      {
         // 创建关闭按钮
         var closeBtn:Sprite = new Sprite();
         closeBtn.graphics.beginFill(0xCC0000);
         closeBtn.graphics.lineStyle(1, 0xFF0000);
         closeBtn.graphics.drawRect(0, 0, 60, 25);
         closeBtn.graphics.endFill();
         closeBtn.x = 730;
         closeBtn.y = 10;
         closeBtn.buttonMode = true;
         closeBtn.mouseChildren = false;

         var closeTxt:TextField = new TextField();
         closeTxt.width = 60;
         closeTxt.height = 25;
         closeTxt.htmlText = "<font color='#FFFFFF' size='12'><b>关闭</b></font>";
         closeTxt.mouseEnabled = false;
         closeBtn.addChild(closeTxt);

         closeBtn.addEventListener("click", function(e:*):void {
            hide();
         });

         addChild(closeBtn);
      }

      private function createCheatingButtons() : void
      {
         var startY:int = 50;
         var buttonHeight:int = 22;
         var spacing:int = 3;
         var currentY:int = startY;
         var buttonWidth:int = 95;

         // 第一行：玩家相关
         this.createSectionTitle("玩家相关", 10, currentY);
         currentY += 20;
         this.createButton("添加金币", "addMoney", 10, currentY, buttonWidth, buttonHeight);
         this.createButton("添加经验", "addExp", 115, currentY, buttonWidth, buttonHeight);
         this.createButton("设置99级", "setLevel99", 220, currentY, buttonWidth, buttonHeight);
         this.createButton("战力999倍", "godMode", 325, currentY, buttonWidth, buttonHeight);
         this.createButton("添加积分", "addScore", 430, currentY, buttonWidth, buttonHeight);
         this.createButton("清除双倍", "clearDouble", 535, currentY, buttonWidth, buttonHeight);
         this.createButton("解除作弊", "noCheat", 640, currentY, buttonWidth, buttonHeight);

         // 第二行：基础物品
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("基础物品", 10, currentY);
         currentY += 20;
         this.createButton("添加常规物品", "addAllItems", 10, currentY, 110, buttonHeight);
         this.createButton("添加所有钥匙", "addAllKeys", 130, currentY, 110, buttonHeight);
         this.createButton("添加黑色武器", "addBlackArms", 250, currentY, 110, buttonHeight);
         this.createButton("设置背包位", "setBagLock", 370, currentY, 110, buttonHeight);
         this.createButton("清理溢出物品", "delOverflow", 490, currentY, 110, buttonHeight);
         this.createButton("强化等级", "setStrengthen", 610, currentY, 110, buttonHeight);

         // 第三行：零件管理
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("零件管理", 10, currentY);
         currentY += 20;
         this.createButton("普通零件30级", "addNormalParts", 10, currentY, 110, buttonHeight);
         this.createButton("普通零件93级", "addMaxParts", 130, currentY, 110, buttonHeight);
         this.createButton("添加特殊零件", "addSpecialParts", 250, currentY, 110, buttonHeight);
         this.createButton("添加稀有零件", "addRareParts", 370, currentY, 110, buttonHeight);
         this.createButton("全部零件30级", "addAllPartsTypes", 490, currentY, 110, buttonHeight);
         this.createButton("自定义零件", "addCustomParts", 610, currentY, 110, buttonHeight);

         // 第四行：背包管理
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("背包管理", 10, currentY);
         currentY += 20;
         this.createButton("清空当前背包", "clearBag", 10, currentY, 110, buttonHeight);
         this.createButton("清空当前仓库", "clearHouse", 130, currentY, 110, buttonHeight);
         this.createButton("按时间排序", "sortByTime", 250, currentY, 110, buttonHeight);
         this.createButton("生成价值物品", "genValueItems", 370, currentY, 110, buttonHeight);
         this.createButton("设置背包位", "setBagLock", 490, currentY, 110, buttonHeight);
         this.createButton("清理溢出物品", "delOverflow", 610, currentY, 110, buttonHeight);

         // 第五行：关卡相关
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("关卡相关", 10, currentY);
         currentY += 20;
         this.createButton("解锁所有地图", "unlockLevels", 10, currentY, 110, buttonHeight);
         this.createButton("通关所有地图", "winAllMaps", 130, currentY, 110, buttonHeight);
         this.createButton("解锁所有难度", "unlockAllDiff", 250, currentY, 110, buttonHeight);
         this.createButton("开启所有扫荡", "openSweeping", 370, currentY, 110, buttonHeight);
         this.createButton("杀死所有队友", "killPartners", 490, currentY, 110, buttonHeight);
         this.createButton("解锁技能系统", "unlockSkill", 610, currentY, 110, buttonHeight);

         // 第六行：秘境技能
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("秘境技能", 10, currentY);
         currentY += 20;
         this.createButton("解锁所有秘境", "unlockWilder", 10, currentY, 110, buttonHeight);
         this.createButton("解锁BOSS编辑", "unlockBossEdit", 130, currentY, 110, buttonHeight);
         this.createButton("添加技能熟练", "addProfi", 250, currentY, 110, buttonHeight);
         this.createButton("设置技能熟练", "setProfi", 370, currentY, 110, buttonHeight);
         this.createButton("删除当前技能", "clearSkill", 490, currentY, 110, buttonHeight);
         this.createButton("显示玩家技能", "showSkills", 610, currentY, 110, buttonHeight);

         // 第七行：存档相关
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("存档相关", 10, currentY);
         currentY += 20;
         this.createButton("初始化存档", "initSave", 10, currentY, 110, buttonHeight);
         this.createButton("复制存档", "getSave", 130, currentY, 110, buttonHeight);
         this.createButton("复制4399存档", "get4399Save", 250, currentY, 110, buttonHeight);
         this.createButton("存档补充处理", "suppleSave", 370, currentY, 110, buttonHeight);
         this.createButton("保存存档", "saveGame", 490, currentY, 110, buttonHeight);
         this.createButton("登出", "logout", 610, currentY, 110, buttonHeight);

         // 第八行：UI和其他
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("UI和其他", 10, currentY);
         currentY += 20;
         this.createButton("隐藏UI", "hideUI", 10, currentY, buttonWidth, buttonHeight);
         this.createButton("显示引导", "showGuide", 115, currentY, buttonWidth, buttonHeight);
         this.createButton("显示统计", "showStats", 220, currentY, buttonWidth, buttonHeight);
         this.createButton("测试升级BUG", "testUpBug", 325, currentY, buttonWidth, buttonHeight);
         this.createButton("关闭作弊判断", "closeCheat", 430, currentY, buttonWidth, buttonHeight);
         this.createButton("指令帮助", "showHelp", 535, currentY, buttonWidth, buttonHeight);
         this.createButton("关闭面板", "closePanel", 640, currentY, buttonWidth, buttonHeight);

         // 添加高级帮助按钮
         currentY += buttonHeight + spacing + 5;
         this.createButton("高级帮助", "showAdvHelp", 10, currentY, 100, buttonHeight);

         // 创建输入区域
         currentY += buttonHeight + spacing * 2 + 10;
         this.createInputArea(currentY);
      }

      private function createSectionTitle(title:String, x:int, y:int) : void
      {
         var titleTxt:TextField = new TextField();
         titleTxt.x = x;
         titleTxt.y = y;
         titleTxt.width = 200;
         titleTxt.height = 18;
         titleTxt.htmlText = "<font color='#FFFF00' size='14'><b>" + title + ":</b></font>";
         addChild(titleTxt);
      }

      private function createButton(label:String, action:String, x:int, y:int, width:int, height:int) : void
      {
         var btn:Sprite = new Sprite();
         btn.graphics.beginFill(0x666666);
         btn.graphics.lineStyle(1, 0x999999);
         btn.graphics.drawRect(0, 0, width, height);
         btn.graphics.endFill();
         btn.x = x;
         btn.y = y;
         btn.buttonMode = true;
         btn.mouseChildren = false;

         var txt:TextField = new TextField();
         txt.width = width;
         txt.height = height;
         txt.htmlText = "<font color='#FFFFFF' size='10'>" + label + "</font>";
         txt.mouseEnabled = false;
         btn.addChild(txt);

         btn.addEventListener("click", function(e:*):void {
            handleButtonClick(action);
         });

         addChild(btn);
      }

      private function createInputArea(startY:int) : void
      {
         // 创建输入标签
         var inputLabel:TextField = new TextField();
         inputLabel.x = 10;
         inputLabel.y = startY;
         inputLabel.width = 200;
         inputLabel.height = 20;
         inputLabel.htmlText = "<font color='#FFFFFF'>输入作弊指令:</font>";
         addChild(inputLabel);

         // 创建输入框背景
         var inputBg:Sprite = new Sprite();
         inputBg.graphics.beginFill(0x222222);
         inputBg.graphics.lineStyle(1, 0x666666);
         inputBg.graphics.drawRect(0, 0, 300, 25);
         inputBg.graphics.endFill();
         inputBg.x = 10;
         inputBg.y = startY + 25;
         addChild(inputBg);

         // 创建输入框
         var inputField:TextField = new TextField();
         inputField.x = 15;
         inputField.y = startY + 30;
         inputField.width = 290;
         inputField.height = 15;
         inputField.type = "input";
         inputField.background = false;
         inputField.border = false;
         inputField.textColor = 0xFFFFFF;
         inputField.addEventListener("keyDown", function(e:*):void {
            if (e.keyCode == 13) { // Enter键
               handleInputCommand(inputField.text);
               inputField.text = "";
            }
         });
         addChild(inputField);

         // 创建执行按钮
         this.createButton("执行", "executeInput", 320, startY + 25, 60, 25);
      }

      private function handleButtonClick(action:String) : void
      {
         switch(action) {
            // 玩家相关
            case "addMoney":
               Gaming.testCtrl.cheating.doOrder("player", "addCoin", "", 10000);
               break;
            case "addExp":
               Gaming.testCtrl.cheating.doOrder("player", "addHeroExp", "", 1000);
               break;
            case "setLevel99":
               Gaming.testCtrl.cheating.doOrder("player", "setHeroLv", "", 99);
               break;
            case "godMode":
               Gaming.testCtrl.cheating.doOrder("level", "setDpsMul", "", 999);
               break;
            case "addScore":
               Gaming.testCtrl.cheating.doOrder("player", "addSore", "", 10000);
               break;
            case "clearDouble":
               Gaming.testCtrl.cheating.doOrder("player", "clearAllDoubleAdd", "", 0);
               break;
            case "noCheat":
               Gaming.testCtrl.cheating.doOrder("player", "noZuobi", "", 0);
               break;

            // 物品装备
            case "addAllItems":
               Gaming.testCtrl.cheating.doOrder("things", "addAllThings", "", 99);
               break;
            case "addAllKeys":
               Gaming.testCtrl.cheating.doOrder("things", "addAllKey", "", 10);
               break;
            case "addBlackArms":
               Gaming.testCtrl.cheating.doOrder("equip", "addBlackArms", "", 0);
               break;
            case "setBagLock":
               Gaming.testCtrl.cheating.doOrder("things", "setBagLock", "", 100);
               break;
            case "delOverflow":
               Gaming.testCtrl.cheating.doOrder("things", "delNoPositionThings", "", 0);
               break;
            case "setStrengthen":
               Gaming.testCtrl.cheating.doOrder("equip", "setStrengthenLv", "", 10);
               break;

            // 零件管理
            case "addNormalParts":
               Gaming.testCtrl.cheating.doOrder("things", "addPartsAll", "30,10", 0);
               break;
            case "addMaxParts":
               Gaming.testCtrl.cheating.doOrder("things", "addPartsAll", "93,10", 0);
               break;
            case "addSpecialParts":
               Gaming.testCtrl.cheating.doOrder("things", "addSpecialParts", "10", 0);
               break;
            case "addRareParts":
               Gaming.testCtrl.cheating.doOrder("things", "addRareParts", "10", 0);
               break;
            case "addAllPartsTypes":
               Gaming.testCtrl.cheating.doOrder("things", "addAllPartsTypes", "30,10", 0);
               break;
            case "addCustomParts":
               // 默认添加一些常用的高级零件
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "bulletParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "shooterParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "capacityParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "loaderParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "stablerParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "sightParts,60,5", 0);
               break;

            // 背包管理
            case "clearBag":
               Gaming.testCtrl.cheating.doOrder("things", "clearBag", "", 0);
               break;
            case "clearHouse":
               Gaming.testCtrl.cheating.doOrder("things", "clearHouse", "", 0);
               break;
            case "sortByTime":
               Gaming.testCtrl.cheating.doOrder("things", "sortByTime", "", 0);
               break;
            case "genValueItems":
               Gaming.testCtrl.cheating.doOrder("things", "getThingsStrByPrice", "", 10000);
               break;

            // 关卡相关
            case "unlockLevels":
               Gaming.testCtrl.cheating.doOrder("level", "unlockAllMap", "", 0);
               break;
            case "winAllMaps":
               Gaming.testCtrl.cheating.doOrder("level", "winAllMap", "", 0);
               break;
            case "unlockAllDiff":
               Gaming.testCtrl.cheating.doOrder("level", "unlockAllDiff", "", 0);
               break;
            case "openSweeping":
               Gaming.testCtrl.cheating.doOrder("level", "openAllSweeping", "", 0);
               break;
            case "killPartners":
               Gaming.testCtrl.cheating.doOrder("level", "killAllPartner", "", 0);
               break;
            case "unlockSkill":
               Gaming.testCtrl.cheating.doOrder("skill", "unlockSkill", "", 0);
               break;

            // 秘境技能
            case "unlockWilder":
               Gaming.testCtrl.cheating.doOrder("wilder", "unlockAllWider", "", 0);
               break;
            case "unlockBossEdit":
               Gaming.testCtrl.cheating.doOrder("wilder", "ulockAllBossEditLevel", "", 0);
               break;
            case "addProfi":
               Gaming.testCtrl.cheating.doOrder("skill", "addProfi", "", 1000);
               break;
            case "setProfi":
               Gaming.testCtrl.cheating.doOrder("skill", "setProfi", "", 9999);
               break;
            case "clearSkill":
               Gaming.testCtrl.cheating.doOrder("skill", "clearNowSkill", "", 0);
               break;
            case "showSkills":
               Gaming.testCtrl.cheating.doOrder("level", "showPlayerSkillText", "", 0);
               break;

            // 存档相关
            case "initSave":
               Gaming.testCtrl.cheating.doOrder("save", "initPlayerSave", "", 0);
               break;
            case "getSave":
               Gaming.testCtrl.cheating.doOrder("save", "getSaveData", "", 0);
               break;
            case "get4399Save":
               Gaming.testCtrl.cheating.doOrder("save", "get4399Save", "", 0);
               break;
            case "suppleSave":
               Gaming.testCtrl.cheating.doOrder("save", "supplePlayerSave", "", 0);
               break;
            case "saveGame":
               Gaming.testCtrl.cheating.doOrder("save", "savePlayerSave", "", 0);
               break;
            case "logout":
               Gaming.testCtrl.cheating.doOrder("save", "logout", "", 0);
               break;

            // UI和其他
            case "hideUI":
               Gaming.testCtrl.cheating.doOrder("ui", "hideUI", "", 0);
               break;
            case "showGuide":
               Gaming.testCtrl.cheating.doOrder("ui", "showGuide", "", 0);
               break;
            case "showStats":
               Gaming.testCtrl.cheating.doOrder("count", "showLevelCount", "", 0);
               break;
            case "testUpBug":
               Gaming.testCtrl.cheating.doOrder("other", "testUplevelBug", "", 0);
               break;
            case "closeCheat":
               Gaming.testCtrl.cheating.doOrder("other", "closezuobipan", "", 0);
               break;
            case "showHelp":
               this.showHelpDialog();
               break;
            case "showAdvHelp":
               this.showAdvancedHelpDialog();
               break;
            case "closePanel":
               this.hide();
               break;
            case "executeInput":
               // 这个会在输入处理中调用
               break;
         }
      }

      private function handleInputCommand(command:String) : void
      {
         if (command && command.length > 0) {
            // 使用公共方法来处理作弊指令
            Gaming.testCtrl.cheating.inputStr(command);
         }
      }

      private function showHelpDialog() : void
      {
         var helpText:String = "=== 作弊功能快速帮助 ===\n\n";
         helpText += "【按钮功能说明】\n";
         helpText += "• 玩家相关: 金币、经验、等级、积分等\n";
         helpText += "• 基础物品: 常规物品、钥匙、武器等\n";
         helpText += "• 零件管理: 普通/特殊/稀有零件，支持任意等级\n";
         helpText += "• 背包管理: 清空、排序、扩容等功能\n";
         helpText += "• 关卡相关: 地图解锁、通关、难度等\n";
         helpText += "• 秘境技能: 秘境、技能、熟练度等\n";
         helpText += "• 存档相关: 存档操作、4399存档等\n";
         helpText += "• UI和其他: 界面、统计、测试等\n\n";

         helpText += "【常用指令格式】\n";
         helpText += "addCoin=10000 - 添加10000银币\n";
         helpText += "setHeroLv=50 - 设置等级为50\n";
         helpText += "addAllThings=10 - 添加常规物品各10个(不含零件)\n";
         helpText += "addPartsAll=60,10 - 添加60级普通零件各10个\n";
         helpText += "addAllPartsTypes=60,10 - 添加全部类型零件\n";
         helpText += "addCustomParts=bulletParts,90,5 - 添加指定零件\n";
         helpText += "setBagLock=200 - 设置背包200个位置\n\n";

         helpText += "【4399存档功能】\n";
         helpText += "• 复制4399存档: 获取4399格式存档数据\n";
         helpText += "• 复制存档: 获取内部格式存档数据\n";
         helpText += "• 存档补充处理: 修复存档兼容性\n\n";

         helpText += "【使用说明】\n";
         helpText += "1. 点击按钮直接执行对应功能\n";
         helpText += "2. 在输入框输入指令按回车执行\n";
         helpText += "3. 点击'高级帮助'查看完整指令列表\n";
         helpText += "4. 大部分功能会显示执行结果";

         this.createHelpWindow(helpText);
      }

      private function showAdvancedHelpDialog() : void
      {
         var helpText:String = "=== 完整作弊指令列表 ===\n\n";
         helpText += "【玩家相关 - player】\n";
         helpText += "addCoin=数值 - 添加银币\n";
         helpText += "addHeroExp=数值 - 添加经验\n";
         helpText += "setHeroLv=数值 - 设置等级\n";
         helpText += "addSore=数值 - 添加积分\n";
         helpText += "setDpsMul=数值 - 设置战力倍数\n";
         helpText += "clearAllDoubleAdd - 清除双倍时间\n";
         helpText += "noZuobi - 解除作弊标记\n";
         helpText += "isZuobi - 设置作弊标记\n";
         helpText += "showZuobi - 显示作弊原因\n\n";

         helpText += "【物品装备 - things/equip】\n";
         helpText += "addAllThings=数值 - 添加常规物品(不含零件)\n";
         helpText += "addAllKey=数值 - 添加所有钥匙\n";
         helpText += "addPartsAll=等级,数量 - 添加指定等级零件\n";
         helpText += "addSpecialParts=数量 - 添加特殊零件\n";
         helpText += "clearBag - 清空当前背包\n";
         helpText += "clearHouse - 清空当前仓库\n";
         helpText += "setBagLock=数值 - 设置背包解锁位置\n";
         helpText += "delNoPositionThings - 清理溢出物品\n";
         helpText += "addBlackArms - 添加所有黑色武器\n";
         helpText += "setStrengthenLv=数值 - 设置强化等级\n";
         helpText += "sortByTime - 按时间排序背包\n";
         helpText += "getThingsStrByPrice=价值 - 生成指定价值物品\n\n";

         helpText += "【关卡相关 - level】\n";
         helpText += "unlockAllMap - 解锁所有地图\n";
         helpText += "winAllMap - 通关所有地图\n";
         helpText += "unlockAllDiff - 解锁所有难度\n";
         helpText += "showPlayerSkillText - 显示玩家技能\n";
         helpText += "killAllPartner - 杀死所有队友\n";
         helpText += "openAllSweeping - 开启所有扫荡\n";
         helpText += "setDpsMul=数值 - 设置战力倍数\n\n";

         helpText += "【秘境技能 - wilder/skill】\n";
         helpText += "unlockAllWider - 解锁所有秘境\n";
         helpText += "setNowWiderNum=数值 - 设置当前秘境次数\n";
         helpText += "ulockAllBossEditLevel - 解锁BOSS编辑\n";
         helpText += "unlockSkill - 解锁技能系统\n";
         helpText += "addProfi=数值 - 添加技能熟练度\n";
         helpText += "setProfi=数值 - 设置技能熟练度\n";
         helpText += "clearNowSkill - 删除当前技能\n\n";

         helpText += "【存档相关 - save】\n";
         helpText += "initPlayerSave - 初始化存档\n";
         helpText += "getSaveData - 复制存档到剪贴板\n";
         helpText += "get4399Save - 复制4399存档\n";
         helpText += "get4399SaveNoThin - 复制未缩减4399存档\n";
         helpText += "get4399SaveThin - 复制缩减4399存档\n";
         helpText += "savePlayerSave - 保存存档\n";
         helpText += "supplePlayerSave - 存档补充处理\n";
         helpText += "logout - 登出\n\n";

         helpText += "【UI和其他 - ui/other/count】\n";
         helpText += "hideUI - 隐藏/显示UI\n";
         helpText += "hideStat - 隐藏状态栏\n";
         helpText += "showGuide - 显示引导\n";
         helpText += "showApp=标签 - 显示指定界面\n";
         helpText += "testUplevelBug - 测试升级BUG\n";
         helpText += "closezuobipan - 关闭作弊判断\n";
         helpText += "showLevelCount - 显示统计信息\n";
         helpText += "sendCount - 发送统计事件\n\n";

         helpText += "【使用格式】\n";
         helpText += "方法名=数值 (如: addCoin=50000)\n";
         helpText += "方法名 (无参数的方法)\n";
         helpText += "在输入框输入后按回车或点击执行";

         this.createHelpWindow(helpText);
      }

      private function createHelpWindow(content:String) : void
      {
         // 创建帮助窗口背景
         var helpBg:Sprite = new Sprite();
         helpBg.graphics.beginFill(0x000000, 0.8);
         helpBg.graphics.drawRect(0, 0, 800, 600);
         helpBg.graphics.endFill();
         addChild(helpBg);

         // 创建内容背景
         var contentBg:Sprite = new Sprite();
         contentBg.graphics.beginFill(0x333333, 0.95);
         contentBg.graphics.lineStyle(2, 0x666666);
         contentBg.graphics.drawRect(0, 0, 760, 520);
         contentBg.graphics.endFill();
         contentBg.x = 20;
         contentBg.y = 40;
         helpBg.addChild(contentBg);

         // 创建滚动文本区域
         var helpText:TextField = new TextField();
         helpText.x = 30;
         helpText.y = 50;
         helpText.width = 740;
         helpText.height = 480;
         helpText.multiline = true;
         helpText.wordWrap = true;
         helpText.htmlText = "<font color='#FFFFFF' size='12'>" + content + "</font>";
         helpText.background = false;
         helpBg.addChild(helpText);

         // 创建关闭按钮
         var closeBtn:Sprite = new Sprite();
         closeBtn.graphics.beginFill(0x666666);
         closeBtn.graphics.lineStyle(1, 0x999999);
         closeBtn.graphics.drawRect(0, 0, 80, 30);
         closeBtn.graphics.endFill();
         closeBtn.x = 700;
         closeBtn.y = 10;
         closeBtn.buttonMode = true;

         var closeTxt:TextField = new TextField();
         closeTxt.width = 80;
         closeTxt.height = 30;
         closeTxt.htmlText = "<font color='#FFFFFF' size='12'>关闭</font>";
         closeTxt.mouseEnabled = false;
         closeBtn.addChild(closeTxt);

         closeBtn.addEventListener("click", function(e:*):void {
            removeChild(helpBg);
         });

         helpBg.addChild(closeBtn);
      }
   }
}


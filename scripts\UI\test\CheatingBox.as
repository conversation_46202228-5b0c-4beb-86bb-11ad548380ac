package UI.test
{
   import UI.NormalUICtrl;
   import UI.base.NormalUI;
   import UI.base.box.NormalBox;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.test.CheatingDefine;
   import flash.display.Sprite;
   import flash.events.TextEvent;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class CheatingBox extends NormalUI
   {

      private var boxObj:Object = {};

      private var haveDataB:Boolean = false;

      private var boxTag:Sprite = null;

      private var menuTxt:TextField;

      private var nowLabel:String = "";

      private var nowBox:NormalBox;

      private var nowDefine:CheatingDefine;

      private var contentContainer:Sprite = new Sprite();

      private var scrollMask:Sprite = new Sprite();

      private var scrollBar:Sprite = new Sprite();

      private var scrollThumb:Sprite = new Sprite();

      private var contentHeight:int = 0;

      private var maskHeight:int = 500;

      private var isDragging:Boolean = false;

      private var dragStartY:Number = 0;

      private var scrollStartY:Number = 0;
      
      public function CheatingBox()
      {
         super();
      }
      
      public function initImg() : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("TestUI","cheatingBox");
         if(Boolean(img0)) {
            this.setImg(img0);
         } else {
            // 创建简单的作弊界面
            this.createSimpleCheatingUI();
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["boxTag","menuTxt"];
         super.setImg(img0);
         this.menuTxt.addEventListener(TextEvent.LINK,this.menuClick);
         this.menuTxt.styleSheet = ComMethod.getLinkCss("#CCCCCC","#00FFFF");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         if (!this.haveDataB) {
            this.createSimpleCheatingUI();
            this.haveDataB = true;
         }
      }
      


      private function createSimpleCheatingUI() : void
      {
         // 创建更大的作弊界面
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x333333, 0.9);
         bg.graphics.drawRect(0, 0, 800, 600);
         bg.graphics.endFill();
         addChild(bg);

         // 创建标题
         var titleTxt:TextField = new TextField();
         titleTxt.x = 10;
         titleTxt.y = 10;
         titleTxt.width = 700;
         titleTxt.height = 30;
         titleTxt.htmlText = "<font color='#00FF00' size='16'><b>作弊功能面板 - 增强版 (可滚动)</b></font>";
         addChild(titleTxt);

         // 创建关闭按钮
         this.createCloseButton();

         // 设置滚动容器
         this.setupScrollContainer();

         // 创建功能按钮区域
         this.createCheatingButtons();

         // 创建滚动条
         this.createScrollBar();
      }

      private function createCloseButton() : void
      {
         // 创建关闭按钮
         var closeBtn:Sprite = new Sprite();
         closeBtn.graphics.beginFill(0xCC0000);
         closeBtn.graphics.lineStyle(1, 0xFF0000);
         closeBtn.graphics.drawRect(0, 0, 60, 25);
         closeBtn.graphics.endFill();
         closeBtn.x = 730;
         closeBtn.y = 10;
         closeBtn.buttonMode = true;
         closeBtn.mouseChildren = false;

         var closeTxt:TextField = new TextField();
         closeTxt.width = 60;
         closeTxt.height = 25;
         closeTxt.htmlText = "<font color='#FFFFFF' size='12'><b>关闭</b></font>";
         closeTxt.mouseEnabled = false;
         closeBtn.addChild(closeTxt);

         closeBtn.addEventListener("click", function(e:*):void {
            hide();
         });

         addChild(closeBtn);
      }

      private function setupScrollContainer() : void
      {
         // 设置内容容器
         contentContainer.x = 10;
         contentContainer.y = 50;
         addChild(contentContainer);

         // 创建遮罩
         scrollMask.graphics.beginFill(0xFF0000, 0);
         scrollMask.graphics.drawRect(10, 50, 750, maskHeight);
         scrollMask.graphics.endFill();
         addChild(scrollMask);

         // 应用遮罩
         contentContainer.mask = scrollMask;

         // 添加鼠标滚轮事件
         addEventListener(MouseEvent.MOUSE_WHEEL, onMouseWheel);
      }

      private function createCheatingButtons() : void
      {
         var startY:int = 10;
         var buttonHeight:int = 22;
         var spacing:int = 3;
         var currentY:int = startY;
         var buttonWidth:int = 95;

         // 第一行：玩家相关
         this.createSectionTitle("玩家相关", 10, currentY);
         currentY += 20;
         this.createButton("添加金币", "addMoney", 10, currentY, buttonWidth, buttonHeight);
         this.createButton("添加经验", "addExp", 115, currentY, buttonWidth, buttonHeight);
         this.createButton("设置99级", "setLevel99", 220, currentY, buttonWidth, buttonHeight);
         this.createButton("战力999倍", "godMode", 325, currentY, buttonWidth, buttonHeight);
         this.createButton("添加积分", "addScore", 430, currentY, buttonWidth, buttonHeight);
         this.createButton("清除双倍", "clearDouble", 535, currentY, buttonWidth, buttonHeight);
         this.createButton("解除作弊", "noCheat", 640, currentY, buttonWidth, buttonHeight);

         // 第二行：基础物品
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("基础物品", 10, currentY);
         currentY += 20;
         this.createButton("添加常规物品", "addAllItems", 10, currentY, 110, buttonHeight);
         this.createButton("添加所有钥匙", "addAllKeys", 130, currentY, 110, buttonHeight);
         this.createButton("强化等级", "setStrengthen", 250, currentY, 110, buttonHeight);
         this.createButton("设置背包位", "setBagLock", 370, currentY, 110, buttonHeight);
         this.createButton("清理溢出物品", "delOverflow", 490, currentY, 110, buttonHeight);
         this.createButton("添加武器碎片", "addArmsChip", 610, currentY, 110, buttonHeight);

         // 第三行：武器管理
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("武器管理", 10, currentY);
         currentY += 20;
         this.createButton("黑色武器", "addBlackArms", 10, currentY, 110, buttonHeight);
         this.createButton("稀有武器", "addRareArms", 130, currentY, 110, buttonHeight);
         this.createButton("金色武器", "addDarkgoldArms", 250, currentY, 110, buttonHeight);
         this.createButton("全品质武器", "addAllColorArms", 370, currentY, 110, buttonHeight);
         this.createButton("红色武器", "addRedArms", 490, currentY, 110, buttonHeight);
         this.createButton("紫色武器", "addPurpleArms", 610, currentY, 110, buttonHeight);

         // 第四行：零件管理
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("零件管理", 10, currentY);
         currentY += 20;
         this.createButton("普通零件30级", "addNormalParts", 10, currentY, 110, buttonHeight);
         this.createButton("普通零件93级", "addMaxParts", 130, currentY, 110, buttonHeight);
         this.createButton("添加特殊零件", "addSpecialParts", 250, currentY, 110, buttonHeight);
         this.createButton("添加稀有零件", "addRareParts", 370, currentY, 110, buttonHeight);
         this.createButton("全部零件30级", "addAllPartsTypes", 490, currentY, 110, buttonHeight);
         this.createButton("自定义零件", "addCustomParts", 610, currentY, 110, buttonHeight);

         // 第五行：背包管理
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("背包管理", 10, currentY);
         currentY += 20;
         this.createButton("清空当前背包", "clearBag", 10, currentY, 110, buttonHeight);
         this.createButton("清空当前仓库", "clearHouse", 130, currentY, 110, buttonHeight);
         this.createButton("按时间排序", "sortByTime", 250, currentY, 110, buttonHeight);
         this.createButton("生成价值物品", "genValueItems", 370, currentY, 110, buttonHeight);
         this.createButton("武器创造器", "showArmsCreator", 490, currentY, 110, buttonHeight);
         this.createButton("显示武器图鉴", "showArmsBook", 610, currentY, 110, buttonHeight);

         // 第五行：关卡相关
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("关卡相关", 10, currentY);
         currentY += 20;
         this.createButton("解锁所有地图", "unlockLevels", 10, currentY, 110, buttonHeight);
         this.createButton("通关所有地图", "winAllMaps", 130, currentY, 110, buttonHeight);
         this.createButton("解锁所有难度", "unlockAllDiff", 250, currentY, 110, buttonHeight);
         this.createButton("开启所有扫荡", "openSweeping", 370, currentY, 110, buttonHeight);
         this.createButton("杀死所有队友", "killPartners", 490, currentY, 110, buttonHeight);
         this.createButton("解锁技能系统", "unlockSkill", 610, currentY, 110, buttonHeight);

         // 第六行：秘境技能
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("秘境技能", 10, currentY);
         currentY += 20;
         this.createButton("解锁所有秘境", "unlockWilder", 10, currentY, 110, buttonHeight);
         this.createButton("解锁BOSS编辑", "unlockBossEdit", 130, currentY, 110, buttonHeight);
         this.createButton("添加技能熟练", "addProfi", 250, currentY, 110, buttonHeight);
         this.createButton("设置技能熟练", "setProfi", 370, currentY, 110, buttonHeight);
         this.createButton("删除当前技能", "clearSkill", 490, currentY, 110, buttonHeight);
         this.createButton("显示玩家技能", "showSkills", 610, currentY, 110, buttonHeight);

         // 第七行：存档相关
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("存档相关", 10, currentY);
         currentY += 20;
         this.createButton("初始化存档", "initSave", 10, currentY, 110, buttonHeight);
         this.createButton("复制存档", "getSave", 130, currentY, 110, buttonHeight);
         this.createButton("复制4399存档", "get4399Save", 250, currentY, 110, buttonHeight);
         this.createButton("存档补充处理", "suppleSave", 370, currentY, 110, buttonHeight);
         this.createButton("保存存档", "saveGame", 490, currentY, 110, buttonHeight);
         this.createButton("登出", "logout", 610, currentY, 110, buttonHeight);

         // 第八行：UI和其他
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("UI和其他", 10, currentY);
         currentY += 20;
         this.createButton("隐藏UI", "hideUI", 10, currentY, buttonWidth, buttonHeight);
         this.createButton("显示引导", "showGuide", 115, currentY, buttonWidth, buttonHeight);
         this.createButton("显示统计", "showStats", 220, currentY, buttonWidth, buttonHeight);
         this.createButton("测试升级BUG", "testUpBug", 325, currentY, buttonWidth, buttonHeight);
         this.createButton("关闭作弊判断", "closeCheat", 430, currentY, buttonWidth, buttonHeight);
         this.createButton("指令帮助", "showHelp", 535, currentY, buttonWidth, buttonHeight);
         this.createButton("关闭面板", "closePanel", 640, currentY, buttonWidth, buttonHeight);

         // 第九行：宠物飞船
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("宠物飞船", 10, currentY);
         currentY += 20;
         this.createButton("添加飞船经验", "addCraftExp", 10, currentY, 110, buttonHeight);
         this.createButton("设置飞船等级", "setCraftLv", 130, currentY, 110, buttonHeight);
         this.createButton("清除所有宠物", "clearAllPet", 250, currentY, 110, buttonHeight);
         this.createButton("设置宠物等级", "setPetLv", 370, currentY, 110, buttonHeight);
         this.createButton("添加基因概率", "addGenePro", 490, currentY, 110, buttonHeight);

         // 第十行：成就系统
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("成就系统", 10, currentY);
         currentY += 20;
         this.createButton("完成所有成就", "completeAllAchieve", 10, currentY, 110, buttonHeight);
         this.createButton("重置当前成就", "resetAchieve", 130, currentY, 110, buttonHeight);
         this.createButton("创建BOSS卡", "createBossCard", 250, currentY, 110, buttonHeight);
         this.createButton("添加装备碎片", "addEquipChip", 370, currentY, 110, buttonHeight);
         this.createButton("添加所有套装", "addAllSuit", 490, currentY, 110, buttonHeight);
         this.createButton("添加所有时装", "addAllFashion", 610, currentY, 110, buttonHeight);

         // 第十一行：系统功能
         currentY += buttonHeight + spacing + 15;
         this.createSectionTitle("系统功能", 10, currentY);
         currentY += 20;
         this.createButton("设置帧数60", "setFrame60", 10, currentY, 110, buttonHeight);
         this.createButton("设置帧数120", "setFrame120", 130, currentY, 110, buttonHeight);
         this.createButton("本地时间开关", "toggleLocalTime", 250, currentY, 110, buttonHeight);
         this.createButton("新的一天", "newDay", 370, currentY, 110, buttonHeight);
         this.createButton("自动存档设置", "setAutoSave", 490, currentY, 110, buttonHeight);

         // 添加高级帮助按钮
         currentY += buttonHeight + spacing + 5;
         this.createButton("高级帮助", "showAdvHelp", 10, currentY, 100, buttonHeight);

         // 创建输入区域
         currentY += buttonHeight + spacing * 2 + 10;
         this.createInputArea(currentY);

         // 创建滚动条
         this.createScrollBar();
      }

      private function createSectionTitle(title:String, x:int, y:int) : void
      {
         var titleTxt:TextField = new TextField();
         titleTxt.x = x;
         titleTxt.y = y;
         titleTxt.width = 200;
         titleTxt.height = 18;
         titleTxt.htmlText = "<font color='#FFFF00' size='14'><b>" + title + ":</b></font>";
         contentContainer.addChild(titleTxt);
      }

      private function createButton(label:String, action:String, x:int, y:int, width:int, height:int) : void
      {
         var btn:Sprite = new Sprite();
         btn.graphics.beginFill(0x666666);
         btn.graphics.lineStyle(1, 0x999999);
         btn.graphics.drawRect(0, 0, width, height);
         btn.graphics.endFill();
         btn.x = x;
         btn.y = y;
         btn.buttonMode = true;
         btn.mouseChildren = false;

         var txt:TextField = new TextField();
         txt.width = width;
         txt.height = height;
         txt.htmlText = "<font color='#FFFFFF' size='10'>" + label + "</font>";
         txt.mouseEnabled = false;
         btn.addChild(txt);

         btn.addEventListener("click", function(e:*):void {
            handleButtonClick(action);
         });

         contentContainer.addChild(btn);
      }

      private function createInputArea(startY:int) : void
      {
         // 创建输入标签
         var inputLabel:TextField = new TextField();
         inputLabel.x = 10;
         inputLabel.y = startY;
         inputLabel.width = 200;
         inputLabel.height = 20;
         inputLabel.htmlText = "<font color='#FFFFFF'>输入作弊指令:</font>";
         contentContainer.addChild(inputLabel);

         // 创建输入框背景
         var inputBg:Sprite = new Sprite();
         inputBg.graphics.beginFill(0x222222);
         inputBg.graphics.lineStyle(1, 0x666666);
         inputBg.graphics.drawRect(0, 0, 300, 25);
         inputBg.graphics.endFill();
         inputBg.x = 10;
         inputBg.y = startY + 25;
         contentContainer.addChild(inputBg);

         // 创建输入框
         var inputField:TextField = new TextField();
         inputField.name = "inputField"; // 添加名称以便查找
         inputField.x = 15;
         inputField.y = startY + 30;
         inputField.width = 290;
         inputField.height = 15;
         inputField.type = "input";
         inputField.background = false;
         inputField.border = false;
         inputField.textColor = 0xFFFFFF;
         inputField.addEventListener("keyDown", function(e:*):void {
            if (e.keyCode == 13) { // Enter键
               handleInputCommand(inputField.text);
               inputField.text = "";
            }
         });
         contentContainer.addChild(inputField);

         // 创建执行按钮
         this.createButton("执行", "executeInput", 320, startY + 25, 60, 25);

         // 记录内容总高度
         contentHeight = startY + 60;
      }

      private function createScrollBar() : void
      {
         // 只有当内容高度超过遮罩高度时才显示滚动条
         if (contentHeight <= maskHeight) {
            return;
         }

         // 创建滚动条背景
         scrollBar.graphics.beginFill(0x444444);
         scrollBar.graphics.drawRect(0, 0, 15, maskHeight);
         scrollBar.graphics.endFill();
         scrollBar.x = 765;
         scrollBar.y = 50;
         addChild(scrollBar);

         // 创建滚动条滑块
         var thumbHeight:int = Math.max(20, maskHeight * maskHeight / contentHeight);
         scrollThumb.graphics.beginFill(0x888888);
         scrollThumb.graphics.drawRect(0, 0, 15, thumbHeight);
         scrollThumb.graphics.endFill();
         scrollThumb.x = 0;
         scrollThumb.y = 0;
         scrollThumb.buttonMode = true;
         scrollBar.addChild(scrollThumb);

         // 添加滑块拖拽事件
         scrollThumb.addEventListener(MouseEvent.MOUSE_DOWN, onThumbMouseDown);
      }

      private function onMouseWheel(e:MouseEvent) : void
      {
         if (contentHeight <= maskHeight) {
            return;
         }

         var scrollAmount:Number = e.delta * 20;
         var baseY:Number = 50; // 基础Y位置
         var currentOffset:Number = contentContainer.y - baseY;
         var newOffset:Number = currentOffset + scrollAmount;
         var minOffset:Number = -(contentHeight - maskHeight);

         newOffset = Math.max(minOffset, Math.min(0, newOffset));
         contentContainer.y = baseY + newOffset;

         updateScrollThumb();
      }

      private function onThumbMouseDown(e:MouseEvent) : void
      {
         isDragging = true;
         dragStartY = e.stageY;
         scrollStartY = scrollThumb.y;

         stage.addEventListener(MouseEvent.MOUSE_MOVE, onThumbMouseMove);
         stage.addEventListener(MouseEvent.MOUSE_UP, onThumbMouseUp);
      }

      private function onThumbMouseMove(e:MouseEvent) : void
      {
         if (!isDragging) return;

         var deltaY:Number = e.stageY - dragStartY;
         var newThumbY:Number = scrollStartY + deltaY;
         var maxThumbY:Number = maskHeight - scrollThumb.height;

         newThumbY = Math.max(0, Math.min(maxThumbY, newThumbY));
         scrollThumb.y = newThumbY;

         // 根据滑块位置更新内容位置
         var scrollRatio:Number = newThumbY / maxThumbY;
         var maxContentOffset:Number = -(contentHeight - maskHeight);
         var baseY:Number = 50;
         contentContainer.y = baseY + (maxContentOffset * scrollRatio);
      }

      private function onThumbMouseUp(e:MouseEvent) : void
      {
         isDragging = false;

         stage.removeEventListener(MouseEvent.MOUSE_MOVE, onThumbMouseMove);
         stage.removeEventListener(MouseEvent.MOUSE_UP, onThumbMouseUp);
      }

      private function updateScrollThumb() : void
      {
         if (contentHeight <= maskHeight || !scrollThumb.parent) {
            return;
         }

         var baseY:Number = 50;
         var currentOffset:Number = contentContainer.y - baseY;
         var scrollRatio:Number = -currentOffset / (contentHeight - maskHeight);
         var maxThumbY:Number = maskHeight - scrollThumb.height;
         scrollThumb.y = scrollRatio * maxThumbY;
      }

      private function handleButtonClick(action:String) : void
      {
         switch(action) {
            // 玩家相关
            case "addMoney":
               Gaming.testCtrl.cheating.doOrder("player", "addCoin", "", 10000);
               Gaming.uiGroup.alertBox.show("添加金币", "已添加10000金币");
               break;
            case "addExp":
               Gaming.testCtrl.cheating.doOrder("player", "addHeroExp", "", 1000);
               Gaming.uiGroup.alertBox.show("添加经验", "已添加1000经验值");
               break;
            case "setLevel99":
               Gaming.testCtrl.cheating.doOrder("player", "setHeroLv", "", 99);
               Gaming.uiGroup.alertBox.show("设置等级", "已将等级设置为99级");
               break;
            case "godMode":
               Gaming.testCtrl.cheating.doOrder("level", "setDpsMul", "", 999);
               Gaming.uiGroup.alertBox.show("战力提升", "已将伤害倍数设置为999倍");
               break;
            case "addScore":
               Gaming.testCtrl.cheating.doOrder("player", "addSore", "", 10000);
               break;
            case "clearDouble":
               Gaming.testCtrl.cheating.doOrder("player", "clearAllDoubleAdd", "", 0);
               break;
            case "noCheat":
               Gaming.testCtrl.cheating.doOrder("player", "noZuobi", "", 0);
               break;

            // 物品装备
            case "addAllItems":
               Gaming.testCtrl.cheating.doOrder("things", "addAllThings", "", 99);
               break;
            case "addAllKeys":
               Gaming.testCtrl.cheating.doOrder("things", "addAllKey", "", 10);
               break;
            case "setBagLock":
               Gaming.testCtrl.cheating.doOrder("things", "setBagLock", "", 100);
               break;
            case "delOverflow":
               Gaming.testCtrl.cheating.doOrder("things", "delNoPositionThings", "", 0);
               break;
            case "setStrengthen":
               Gaming.testCtrl.cheating.doOrder("equip", "setStrengthenLv", "", 27);
               Gaming.uiGroup.alertBox.show("强化等级", "已将装备强化等级设置为27级");
               break;
            case "addArmsChip":
               Gaming.testCtrl.cheating.doOrder("equip", "addArmsChip", "", 10);
               break;

            // 武器管理
            case "addBlackArms":
               Gaming.testCtrl.cheating.doOrder("equip", "addBlackArms", "", 0);
               break;
            case "addRareArms":
               Gaming.testCtrl.cheating.doOrder("equip", "addRareArms", "", 0);
               break;
            case "addDarkgoldArms":
               Gaming.testCtrl.cheating.doOrder("equip", "addDarkgoldArms", "", 0);
               Gaming.uiGroup.alertBox.show("金色武器", "已添加所有金色品质武器到背包");
               break;
            case "addAllColorArms":
               Gaming.testCtrl.cheating.doOrder("equip", "addAllColorArms", "", 0);
               break;
            case "addRedArms":
               Gaming.testCtrl.cheating.doOrder("equip", "addCustomColorArms", "red", 0);
               break;
            case "addPurpleArms":
               Gaming.testCtrl.cheating.doOrder("equip", "addCustomColorArms", "purple", 0);
               break;

            // 零件管理
            case "addNormalParts":
               Gaming.testCtrl.cheating.doOrder("things", "addPartsAll", "30,10", 0);
               break;
            case "addMaxParts":
               Gaming.testCtrl.cheating.doOrder("things", "addPartsAll", "93,10", 0);
               break;
            case "addSpecialParts":
               Gaming.testCtrl.cheating.doOrder("things", "addSpecialParts", "10", 0);
               break;
            case "addRareParts":
               Gaming.testCtrl.cheating.doOrder("things", "addRareParts", "10", 0);
               break;
            case "addAllPartsTypes":
               Gaming.testCtrl.cheating.doOrder("things", "addAllPartsTypes", "30,10", 0);
               break;
            case "addCustomParts":
               // 默认添加一些常用的高级零件
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "bulletParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "shooterParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "capacityParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "loaderParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "stablerParts,60,5", 0);
               Gaming.testCtrl.cheating.doOrder("things", "addCustomParts", "sightParts,60,5", 0);
               break;

            // 背包管理
            case "clearBag":
               Gaming.testCtrl.cheating.doOrder("things", "clearBag", "", 0);
               break;
            case "clearHouse":
               Gaming.testCtrl.cheating.doOrder("things", "clearHouse", "", 0);
               break;
            case "sortByTime":
               Gaming.testCtrl.cheating.doOrder("things", "sortByTime", "", 0);
               break;
            case "genValueItems":
               Gaming.testCtrl.cheating.doOrder("things", "getThingsStrByPrice", "", 10000);
               break;
            case "showArmsCreator":
               Gaming.testCtrl.cheating.doOrder("equip", "showArmsCreator", "", 0);
               break;
            case "showArmsBook":
               // 显示武器选择界面
               this.showWeaponSelectionDialog();
               break;

            // 关卡相关
            case "unlockLevels":
               Gaming.testCtrl.cheating.doOrder("level", "unlockAllMap", "", 0);
               break;
            case "winAllMaps":
               Gaming.testCtrl.cheating.doOrder("level", "winAllMap", "", 0);
               break;
            case "unlockAllDiff":
               Gaming.testCtrl.cheating.doOrder("level", "unlockAllDiff", "", 0);
               break;
            case "openSweeping":
               Gaming.testCtrl.cheating.doOrder("level", "openAllSweeping", "", 0);
               break;
            case "killPartners":
               Gaming.testCtrl.cheating.doOrder("level", "killAllPartner", "", 0);
               break;
            case "unlockSkill":
               Gaming.testCtrl.cheating.doOrder("skill", "unlockSkill", "", 0);
               break;

            // 秘境技能
            case "unlockWilder":
               Gaming.testCtrl.cheating.doOrder("wilder", "unlockAllWider", "", 0);
               break;
            case "unlockBossEdit":
               Gaming.testCtrl.cheating.doOrder("wilder", "ulockAllBossEditLevel", "", 0);
               break;
            case "addProfi":
               Gaming.testCtrl.cheating.doOrder("skill", "addProfi", "", 1000);
               break;
            case "setProfi":
               Gaming.testCtrl.cheating.doOrder("skill", "setProfi", "", 9999);
               break;
            case "clearSkill":
               Gaming.testCtrl.cheating.doOrder("skill", "clearNowSkill", "", 0);
               break;
            case "showSkills":
               Gaming.testCtrl.cheating.doOrder("level", "showPlayerSkillText", "", 0);
               break;

            // 存档相关
            case "initSave":
               Gaming.testCtrl.cheating.doOrder("save", "initPlayerSave", "", 0);
               break;
            case "getSave":
               Gaming.testCtrl.cheating.doOrder("save", "getSaveData", "", 0);
               break;
            case "get4399Save":
               Gaming.testCtrl.cheating.doOrder("save", "get4399Save", "", 0);
               break;
            case "suppleSave":
               Gaming.testCtrl.cheating.doOrder("save", "supplePlayerSave", "", 0);
               break;
            case "saveGame":
               Gaming.testCtrl.cheating.doOrder("save", "savePlayerSave", "", 0);
               break;
            case "logout":
               Gaming.testCtrl.cheating.doOrder("save", "logout", "", 0);
               break;

            // UI和其他
            case "hideUI":
               Gaming.testCtrl.cheating.doOrder("ui", "hideUI", "", 0);
               break;
            case "showGuide":
               Gaming.testCtrl.cheating.doOrder("ui", "showGuide", "", 0);
               break;
            case "showStats":
               Gaming.testCtrl.cheating.doOrder("count", "showLevelCount", "", 0);
               break;
            case "testUpBug":
               Gaming.testCtrl.cheating.doOrder("other", "testUplevelBug", "", 0);
               break;
            case "closeCheat":
               Gaming.testCtrl.cheating.doOrder("other", "closezuobipan", "", 0);
               break;
            case "showHelp":
               this.showHelpDialog();
               break;
            case "showAdvHelp":
               this.showAdvancedHelpDialog();
               break;
            case "closePanel":
               this.hide();
               break;

            // 宠物飞船相关
            case "addCraftExp":
               Gaming.testCtrl.cheating.doOrder("pet", "addNowCraftExp", "", 10000);
               Gaming.uiGroup.alertBox.show("飞船经验", "已添加10000飞船经验值");
               break;
            case "setCraftLv":
               Gaming.testCtrl.cheating.doOrder("pet", "setNowCraftLv", "", 99);
               Gaming.uiGroup.alertBox.show("飞船等级", "已将飞船等级设置为99级");
               break;
            case "clearAllPet":
               Gaming.testCtrl.cheating.doOrder("pet", "clearAllPet", "", 0);
               Gaming.uiGroup.alertBox.show("清除宠物", "已清除所有宠物");
               break;
            case "setPetLv":
               Gaming.testCtrl.cheating.doOrder("pet", "setPetLv", "", 99);
               Gaming.uiGroup.alertBox.show("宠物等级", "已将当前宠物等级设置为99级");
               break;
            case "addGenePro":
               Gaming.testCtrl.cheating.doOrder("pet", "addNowGenePro", "", 100);
               Gaming.uiGroup.alertBox.show("基因概率", "已添加当前基因100%概率");
               break;

            // 成就系统相关
            case "completeAllAchieve":
               Gaming.testCtrl.cheating.doOrder("achieve", "completeAllAchieve", "", 0);
               Gaming.uiGroup.alertBox.show("完成成就", "已完成所有成就！");
               break;
            case "resetAchieve":
               Gaming.testCtrl.cheating.doOrder("achieve", "reachieve", "", 0);
               Gaming.uiGroup.alertBox.show("重置成就", "已重置当前类别的成就状态");
               break;
            case "createBossCard":
               Gaming.testCtrl.cheating.doOrder("achieve", "createBosscard", "测试BOSS 5", 0);
               Gaming.uiGroup.alertBox.show("创建BOSS卡", "已创建测试BOSS卡片");
               break;
            case "addEquipChip":
               Gaming.testCtrl.cheating.doOrder("equip", "addEquipChip", "", 10);
               Gaming.uiGroup.alertBox.show("装备碎片", "已添加所有装备碎片10个");
               break;
            case "addAllSuit":
               Gaming.testCtrl.cheating.doOrder("equip", "addSuit", "", 0);
               Gaming.uiGroup.alertBox.show("添加套装", "已添加所有套装");
               break;
            case "addAllFashion":
               Gaming.testCtrl.cheating.doOrder("equip", "addAllFashion", "", 0);
               Gaming.uiGroup.alertBox.show("添加时装", "已添加所有时装");
               break;

            // 系统功能相关
            case "setFrame60":
               Gaming.testCtrl.cheating.doOrder("system", "setFrame", "", 60);
               Gaming.uiGroup.alertBox.show("设置帧数", "已将帧数设置为60FPS");
               break;
            case "setFrame120":
               Gaming.testCtrl.cheating.doOrder("system", "setFrame", "", 120);
               Gaming.uiGroup.alertBox.show("设置帧数", "已将帧数设置为120FPS");
               break;
            case "toggleLocalTime":
               Gaming.testCtrl.cheating.doOrder("system", "setToLocalTime", "", 0);
               Gaming.uiGroup.alertBox.show("本地时间", "已切换本地时间开关");
               break;
            case "newDay":
               Gaming.testCtrl.cheating.doOrder("system", "newDay", "", 0);
               Gaming.uiGroup.alertBox.show("新的一天", "已触发新的一天事件");
               break;
            case "setAutoSave":
               Gaming.testCtrl.cheating.doOrder("save", "autoSaveTime", "", 300);
               Gaming.uiGroup.alertBox.show("自动存档", "已将自动存档间隔设置为300秒");
               break;

            case "executeInput":
               // 获取输入框的文本并执行
               var inputField:TextField = contentContainer.getChildByName("inputField") as TextField;
               if(!inputField) {
                  // 如果找不到输入框，尝试在contentContainer中查找
                  for(var i:int = 0; i < contentContainer.numChildren; i++) {
                     var child:* = contentContainer.getChildAt(i);
                     if(child is TextField && TextField(child).type == "input") {
                        inputField = child as TextField;
                        break;
                     }
                  }
               }
               if(inputField && inputField.text && inputField.text.length > 0) {
                  this.handleInputCommand(inputField.text);
                  inputField.text = "";
               }
               break;
         }
      }

      private function handleInputCommand(command:String) : void
      {
         if (command && command.length > 0) {
            // 使用公共方法来处理作弊指令
            Gaming.testCtrl.cheating.inputStr(command);
         }
      }

      private function showHelpDialog() : void
      {
         var helpText:String = "=== 作弊功能快速帮助 ===\n\n";
         helpText += "【按钮功能说明】\n";
         helpText += "• 玩家相关: 金币、经验、等级、积分等\n";
         helpText += "• 基础物品: 常规物品、钥匙、武器碎片等\n";
         helpText += "• 武器管理: 黑色/稀有/金色/全品质武器\n";
         helpText += "• 零件管理: 普通/特殊/稀有零件，支持任意等级\n";
         helpText += "• 背包管理: 清空、排序、扩容、武器创造器\n";
         helpText += "• 关卡相关: 地图解锁、通关、难度等\n";
         helpText += "• 秘境技能: 秘境、技能、熟练度等\n";
         helpText += "• 存档相关: 存档操作、4399存档等\n";
         helpText += "• UI和其他: 界面、统计、测试等\n\n";

         helpText += "【常用指令格式】\n";
         helpText += "addCoin=10000 - 添加10000银币\n";
         helpText += "setHeroLv=50 - 设置等级为50\n";
         helpText += "addAllThings=10 - 添加常规物品各10个(不含零件)\n";
         helpText += "addAllColorArms - 添加全品质武器\n";
         helpText += "addCustomColorArms=orange - 添加橙色武器\n";
         helpText += "addPartsAll=60,10 - 添加60级普通零件各10个\n";
         helpText += "addAllPartsTypes=60,10 - 添加全部类型零件\n";
         helpText += "setBagLock=200 - 设置背包200个位置\n\n";

         helpText += "【4399存档功能】\n";
         helpText += "• 复制4399存档: 获取4399格式存档数据\n";
         helpText += "• 复制存档: 获取内部格式存档数据\n";
         helpText += "• 存档补充处理: 修复存档兼容性\n\n";

         helpText += "【使用说明】\n";
         helpText += "1. 点击按钮直接执行对应功能\n";
         helpText += "2. 在输入框输入指令按回车执行\n";
         helpText += "3. 点击'高级帮助'查看完整指令列表\n";
         helpText += "4. 大部分功能会显示执行结果";

         this.createHelpWindow(helpText);
      }

      private function showAdvancedHelpDialog() : void
      {
         var helpText:String = "=== 完整作弊指令列表 ===\n\n";
         helpText += "【玩家相关 - player】\n";
         helpText += "addCoin=数值 - 添加银币\n";
         helpText += "addHeroExp=数值 - 添加经验\n";
         helpText += "setHeroLv=数值 - 设置等级\n";
         helpText += "addSore=数值 - 添加积分\n";
         helpText += "setDpsMul=数值 - 设置战力倍数\n";
         helpText += "clearAllDoubleAdd - 清除双倍时间\n";
         helpText += "noZuobi - 解除作弊标记\n";
         helpText += "isZuobi - 设置作弊标记\n";
         helpText += "showZuobi - 显示作弊原因\n\n";

         helpText += "【物品装备 - things/equip】\n";
         helpText += "addAllThings=数值 - 添加常规物品(不含零件)\n";
         helpText += "addAllKey=数值 - 添加所有钥匙\n";
         helpText += "addPartsAll=等级,数量 - 添加指定等级零件\n";
         helpText += "addSpecialParts=数量 - 添加特殊零件\n";
         helpText += "clearBag - 清空当前背包\n";
         helpText += "clearHouse - 清空当前仓库\n";
         helpText += "setBagLock=数值 - 设置背包解锁位置\n";
         helpText += "delNoPositionThings - 清理溢出物品\n";
         helpText += "addBlackArms - 添加所有黑色武器\n";
         helpText += "setStrengthenLv=数值 - 设置强化等级(默认27级)\n";
         helpText += "sortByTime - 按时间排序背包\n";
         helpText += "getThingsStrByPrice=价值 - 生成指定价值物品\n";
         helpText += "addEquipChip=数量 - 添加装备碎片\n";
         helpText += "addSuit - 添加所有套装\n";
         helpText += "addAllFashion - 添加所有时装\n\n";

         helpText += "【关卡相关 - level】\n";
         helpText += "unlockAllMap - 解锁所有地图\n";
         helpText += "winAllMap - 通关所有地图\n";
         helpText += "unlockAllDiff - 解锁所有难度\n";
         helpText += "showPlayerSkillText - 显示玩家技能\n";
         helpText += "killAllPartner - 杀死所有队友\n";
         helpText += "openAllSweeping - 开启所有扫荡\n";
         helpText += "setDpsMul=数值 - 设置战力倍数\n\n";

         helpText += "【秘境技能 - wilder/skill】\n";
         helpText += "unlockAllWider - 解锁所有秘境\n";
         helpText += "setNowWiderNum=数值 - 设置当前秘境次数\n";
         helpText += "ulockAllBossEditLevel - 解锁BOSS编辑\n";
         helpText += "unlockSkill - 解锁技能系统\n";
         helpText += "addProfi=数值 - 添加技能熟练度\n";
         helpText += "setProfi=数值 - 设置技能熟练度\n";
         helpText += "clearNowSkill - 删除当前技能\n\n";

         helpText += "【宠物飞船 - pet】\n";
         helpText += "addNowCraftExp=数值 - 添加飞船经验\n";
         helpText += "setNowCraftLv=数值 - 设置飞船等级\n";
         helpText += "clearAllPet - 清除所有宠物\n";
         helpText += "setPetLv=数值 - 设置宠物等级\n";
         helpText += "addNowGenePro=数值 - 添加基因概率\n\n";

         helpText += "【成就系统 - achieve】\n";
         helpText += "completeAllAchieve - 完成所有成就\n";
         helpText += "reachieve - 重置当前成就类别\n";
         helpText += "createBosscard=名称 星级 - 创建BOSS卡\n\n";

         helpText += "【系统功能 - system】\n";
         helpText += "setFrame=数值 - 设置帧数(60/120)\n";
         helpText += "setToLocalTime - 切换本地时间开关\n";
         helpText += "newDay - 触发新的一天\n\n";

         helpText += "【存档相关 - save】\n";
         helpText += "initPlayerSave - 初始化存档\n";
         helpText += "getSaveData - 复制存档到剪贴板\n";
         helpText += "get4399Save - 复制4399存档\n";
         helpText += "get4399SaveNoThin - 复制未缩减4399存档\n";
         helpText += "get4399SaveThin - 复制缩减4399存档\n";
         helpText += "savePlayerSave - 保存存档\n";
         helpText += "supplePlayerSave - 存档补充处理\n";
         helpText += "autoSaveTime=秒数 - 设置自动存档间隔\n";
         helpText += "logout - 登出\n\n";

         helpText += "【UI和其他 - ui/other/count】\n";
         helpText += "hideUI - 隐藏/显示UI\n";
         helpText += "hideStat - 隐藏状态栏\n";
         helpText += "showGuide - 显示引导\n";
         helpText += "showApp=标签 - 显示指定界面\n";
         helpText += "testUplevelBug - 测试升级BUG\n";
         helpText += "closezuobipan - 关闭作弊判断\n";
         helpText += "showLevelCount - 显示统计信息\n";
         helpText += "sendCount - 发送统计事件\n\n";

         helpText += "【使用格式】\n";
         helpText += "方法名=数值 (如: addCoin=50000)\n";
         helpText += "方法名 (无参数的方法)\n";
         helpText += "在输入框输入后按回车或点击执行";

         this.createHelpWindow(helpText);
      }

      private function createHelpWindow(content:String) : void
      {
         // 创建帮助窗口背景
         var helpBg:Sprite = new Sprite();
         helpBg.graphics.beginFill(0x000000, 0.8);
         helpBg.graphics.drawRect(0, 0, 800, 600);
         helpBg.graphics.endFill();
         addChild(helpBg);

         // 创建内容背景
         var contentBg:Sprite = new Sprite();
         contentBg.graphics.beginFill(0x333333, 0.95);
         contentBg.graphics.lineStyle(2, 0x666666);
         contentBg.graphics.drawRect(0, 0, 760, 520);
         contentBg.graphics.endFill();
         contentBg.x = 20;
         contentBg.y = 40;
         helpBg.addChild(contentBg);

         // 创建滚动文本区域
         var helpText:TextField = new TextField();
         helpText.x = 30;
         helpText.y = 50;
         helpText.width = 740;
         helpText.height = 480;
         helpText.multiline = true;
         helpText.wordWrap = true;
         helpText.htmlText = "<font color='#FFFFFF' size='12'>" + content + "</font>";
         helpText.background = false;
         helpBg.addChild(helpText);

         // 创建关闭按钮
         var closeBtn:Sprite = new Sprite();
         closeBtn.graphics.beginFill(0x666666);
         closeBtn.graphics.lineStyle(1, 0x999999);
         closeBtn.graphics.drawRect(0, 0, 80, 30);
         closeBtn.graphics.endFill();
         closeBtn.x = 700;
         closeBtn.y = 10;
         closeBtn.buttonMode = true;

         var closeTxt:TextField = new TextField();
         closeTxt.width = 80;
         closeTxt.height = 30;
         closeTxt.htmlText = "<font color='#FFFFFF' size='12'>关闭</font>";
         closeTxt.mouseEnabled = false;
         closeBtn.addChild(closeTxt);

         closeBtn.addEventListener("click", function(e:*):void {
            removeChild(helpBg);
         });

         helpBg.addChild(closeBtn);
      }

      private function showWeaponSelectionDialog() : void
      {
         // 创建武器选择对话框
         var weaponBg:Sprite = new Sprite();
         weaponBg.graphics.beginFill(0x000000, 0.8);
         weaponBg.graphics.drawRect(0, 0, stage.stageWidth, stage.stageHeight);
         weaponBg.graphics.endFill();
         stage.addChild(weaponBg);

         var dialogBg:Sprite = new Sprite();
         dialogBg.graphics.beginFill(0x333333, 0.95);
         dialogBg.graphics.lineStyle(2, 0x666666);
         dialogBg.graphics.drawRect(0, 0, 600, 500);
         dialogBg.graphics.endFill();
         dialogBg.x = (stage.stageWidth - 600) / 2;
         dialogBg.y = (stage.stageHeight - 500) / 2;
         weaponBg.addChild(dialogBg);

         // 标题
         var titleTxt:TextField = new TextField();
         titleTxt.x = 20;
         titleTxt.y = 10;
         titleTxt.width = 560;
         titleTxt.height = 30;
         titleTxt.htmlText = "<font color='#FFFF00' size='18'><b>武器选择器</b></font>";
         dialogBg.addChild(titleTxt);

         // 武器列表
         var weapons:Array = [
            {name: "新手手枪", id: "1001", desc: "基础武器"},
            {name: "冲锋枪", id: "1002", desc: "快速射击"},
            {name: "狙击枪", id: "1003", desc: "远程精准"},
            {name: "霰弹枪", id: "1004", desc: "近距离高伤害"},
            {name: "火箭筒", id: "1005", desc: "爆炸伤害"},
            {name: "激光枪", id: "1006", desc: "能量武器"},
            {name: "等离子炮", id: "1007", desc: "高科技武器"},
            {name: "黄金AK", id: "2001", desc: "黄金品质"},
            {name: "钻石狙击", id: "2002", desc: "钻石品质"},
            {name: "传说之剑", id: "3001", desc: "传说武器"},
            {name: "神话战锤", id: "3002", desc: "神话武器"},
            {name: "终极毁灭者", id: "4001", desc: "终极武器"}
         ];

         var startY:int = 50;
         var buttonHeight:int = 30;
         var spacing:int = 5;
         var currentY:int = startY;

         for(var i:int = 0; i < weapons.length; i++) {
            var weapon:Object = weapons[i];

            // 创建武器按钮
            var weaponBtn:Sprite = new Sprite();
            weaponBtn.graphics.beginFill(0x555555);
            weaponBtn.graphics.lineStyle(1, 0x888888);
            weaponBtn.graphics.drawRect(0, 0, 560, buttonHeight);
            weaponBtn.graphics.endFill();
            weaponBtn.x = 20;
            weaponBtn.y = currentY;
            weaponBtn.buttonMode = true;
            weaponBtn.mouseChildren = false;

            var weaponTxt:TextField = new TextField();
            weaponTxt.x = 10;
            weaponTxt.y = 5;
            weaponTxt.width = 540;
            weaponTxt.height = 20;
            weaponTxt.htmlText = "<font color='#FFFFFF' size='12'><b>" + weapon.name + "</b> - " + weapon.desc + "</font>";
            weaponTxt.mouseEnabled = false;
            weaponBtn.addChild(weaponTxt);

            // 添加点击事件
            (function(weaponId:String, weaponName:String) {
               weaponBtn.addEventListener("click", function(e:*):void {
                  // 获得武器
                  Gaming.testCtrl.cheating.doOrder("equip", "addSpecificWeapon", weaponId, 1);
                  Gaming.uiGroup.alertBox.show("获得武器", "成功获得: " + weaponName);
                  // 关闭对话框
                  stage.removeChild(weaponBg);
               });
            })(weapon.id, weapon.name);

            dialogBg.addChild(weaponBtn);
            currentY += buttonHeight + spacing;
         }

         // 关闭按钮
         var closeBtn:Sprite = new Sprite();
         closeBtn.graphics.beginFill(0x666666);
         closeBtn.graphics.lineStyle(1, 0x999999);
         closeBtn.graphics.drawRect(0, 0, 80, 30);
         closeBtn.graphics.endFill();
         closeBtn.x = 510;
         closeBtn.y = 460;
         closeBtn.buttonMode = true;
         closeBtn.mouseChildren = false;

         var closeTxt:TextField = new TextField();
         closeTxt.width = 80;
         closeTxt.height = 30;
         closeTxt.htmlText = "<font color='#FFFFFF' size='12'><b>关闭</b></font>";
         closeTxt.mouseEnabled = false;
         closeBtn.addChild(closeTxt);

         closeBtn.addEventListener("click", function(e:*):void {
            stage.removeChild(weaponBg);
         });

         dialogBg.addChild(closeBtn);
      }
   }
}


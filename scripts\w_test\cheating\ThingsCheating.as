package w_test.cheating
{
   import UI.test.SaveTestBox;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.define.PartsConst;
   import dataAll.things.creator.ThingsSemltCreator;
   import dataAll.things.define.ThingsDefine;
   
   public class ThingsCheating extends OneCheating
   {
      
      public function ThingsCheating()
      {
         super();
      }
      
      public function addAllThings(str0:String, v0:int) : String
      {
         var d0:ThingsDefine = null;
         if(v0 < 1)
         {
            v0 = 1;
         }
         var obj0:Object = Gaming.defineGroup.things.obj;
         for each(d0 in obj0)
         {
            if(!d0.isPartsB() && !d0.isShopAutoUseB())
            {
               Gaming.PG.da.thingsBag.addDataByName(d0.name,v0);
            }
         }
         return "添加所有物品：" + v0;
      }
      
      public function addAllKey(str0:String, v0:int) : String
      {
         if(v0 < 1)
         {
            v0 = 1;
         }
         Gaming.PG.da.thingsBag.addDataByName("dreamKey",v0);
         Gaming.PG.da.thingsBag.addDataByName("courageKey",v0);
         Gaming.PG.da.thingsBag.addDataByName("energyKey",v0);
         Gaming.PG.da.thingsBag.addDataByName("victoryKey",v0);
         return "添加所有钥匙：" + v0;
      }
      
      public function addPartsAll(str0:String, v0:int) : String
      {
         var n:* = undefined;
         var name0:String = null;
         var partsBag0:PartsDataGroup = null;
         var numArr0:Array = str0.split(",");
         var arr0:Array = Gaming.defineGroup.things.normalPartsNameArr;
         var lv0:int = int(numArr0[0]);
         var num0:int = int(numArr0[1]);

         // 确保等级是3的倍数
         if(lv0 % 3 != 0) {
            lv0 = int(lv0 / 3) * 3;
         }
         if(lv0 < PartsConst.minLv)
         {
            lv0 = PartsConst.minLv;
         }
         if(lv0 > 93) {
            lv0 = 93;
         }

         for(n in arr0)
         {
            name0 = arr0[n];
            partsBag0 = Gaming.PG.da.partsBag;
            partsBag0.addDataByName(name0 + "_" + lv0,num0);
         }
         return "添加所有" + lv0 + "级普通零件：" + num0 + "个";
      }
      
      public function addSpecialParts(str0:String, v0:int) : String
      {
         var d0:ThingsDefine = null;
         var partsBag0:PartsDataGroup = null;
         var arr0:Array = Gaming.defineGroup.things.fatherArrObj["parts"];
         var numArr0:Array = str0.split(",");
         var num0:int = int(numArr0[0]);
         var count:int = 0;
         for each(d0 in arr0)
         {
            if(d0.isPartsSpecialB())
            {
               if(d0.name.indexOf("_") > 0)
               {
                  partsBag0 = Gaming.PG.da.partsBag;
                  partsBag0.addDataByName(d0.name,num0);
                  count++;
               }
            }
         }
         return "添加特殊零件：" + count + "种，各" + num0 + "个";
      }

      public function addRareParts(str0:String, v0:int) : String
      {
         var d0:ThingsDefine = null;
         var partsBag0:PartsDataGroup = null;
         var arr0:Array = Gaming.defineGroup.things.fatherArrObj["parts"];
         var numArr0:Array = str0.split(",");
         var num0:int = int(numArr0[0]);
         var count:int = 0;
         for each(d0 in arr0)
         {
            if(d0.isPartsRareB())
            {
               partsBag0 = Gaming.PG.da.partsBag;
               partsBag0.addDataByName(d0.name,num0);
               count++;
            }
         }
         return "添加稀有零件：" + count + "种，各" + num0 + "个";
      }

      public function addAllPartsTypes(str0:String, v0:int) : String
      {
         var numArr0:Array = str0.split(",");
         var lv0:int = int(numArr0[0]);
         var num0:int = int(numArr0[1]);

         // 确保等级是3的倍数
         if(lv0 % 3 != 0) {
            lv0 = int(lv0 / 3) * 3;
         }
         if(lv0 < PartsConst.minLv) {
            lv0 = PartsConst.minLv;
         }
         if(lv0 > 93) {
            lv0 = 93;
         }

         // 添加普通零件
         this.addPartsAll(lv0 + "," + num0, 0);
         // 添加特殊零件
         this.addSpecialParts(num0 + "", 0);
         // 添加稀有零件
         this.addRareParts(num0 + "", 0);

         return "添加全部类型零件：" + lv0 + "级普通零件 + 特殊零件 + 稀有零件，各" + num0 + "个";
      }

      public function addCustomParts(str0:String, v0:int) : String
      {
         var params:Array = str0.split(",");
         if(params.length < 3) {
            return "参数错误，格式：零件名,等级,数量";
         }

         var partsName:String = params[0];
         var lv0:int = int(params[1]);
         var num0:int = int(params[2]);

         // 确保等级是3的倍数
         if(lv0 % 3 != 0) {
            lv0 = int(lv0 / 3) * 3;
         }
         if(lv0 < PartsConst.minLv) {
            lv0 = PartsConst.minLv;
         }
         if(lv0 > 93) {
            lv0 = 93;
         }

         var partsBag0:PartsDataGroup = Gaming.PG.da.partsBag;
         partsBag0.addDataByName(partsName + "_" + lv0, num0);

         return "添加" + partsName + "_" + lv0 + "：" + num0 + "个";
      }
      
      public function clearBag(str0:String, v0:int) : String
      {
         var label0:String = Gaming.uiGroup.bagUI.labelBox.nowLabel;
         if(Gaming.uiGroup.skillUI.visible)
         {
            Gaming.uiGroup.skillUI.wearBox.bagBox.fatherData.clearData();
            return "清除了技能背包";
         }
         if(label0 != "")
         {
            Gaming.PG.da[label0 + "Bag"].clearData();
            return "清除了" + label0 + "背包";
         }
         return null;
      }
      
      public function clearHouse(str0:String, v0:int) : String
      {
         var label0:String = Gaming.uiGroup.houseUI.labelBox.nowLabel;
         if(label0 != "")
         {
            Gaming.PG.da[label0 + "House"].clearData();
            return "清除了" + label0 + "仓库";
         }
         return "";
      }
      
      public function setBagLock(str0:String, v0:int) : String
      {
         var label0:String = Gaming.uiGroup.bagUI.labelBox.nowLabel;
         if(label0 != "")
         {
            Gaming.PG.da[label0 + "Bag"].saveGroup.lockLen = v0;
            return "设置当前背包解锁位置数：" + v0;
         }
         return "";
      }
      
      public function sortByTime(str0:String, v0:int) : String
      {
         Gaming.uiGroup.bagUI.sortByTime();
         return "按照日期排序";
      }
      
      public function delNoPositionThings(str0:String, v0:int) : String
      {
         Gaming.PG.DATA.arms.delNoPositionItems();
         return "清除位置溢出的物品";
      }
      
      public function getThingsStrByPrice(str0:String, v0:int) : String
      {
         var s0:String = ThingsSemltCreator.getThingsStrByPrice(v0);
         SaveTestBox.addText(s0);
         return "生成价值为" + v0 + "的物品列表。";
      }
   }
}


package UI.test
{
   import UI.NormalUICtrl;
   import UI.base.HaveConSprite;
   import UI.base.NormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.label.LabelBox;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class TestUI extends HaveConSprite
   {
      
      public var closeBtn:SimpleButton;
      
      public var haveDataB:Boolean = false;
      
      public var saveBox:SaveTestBox = new SaveTestBox();
      
      public var save4399:Save4399Box = new Save4399Box();
      
      public var cheatingBox:CheatingBox = new CheatingBox();
      
      private var labelArr:Array = ["save4399","saveBox","cheatingBox"];
      
      private var labelTag:Sprite;
      
      public var labelBox:LabelBox = new LabelBox();
      
      public function TestUI()
      {
         super();
      }
      
      public function initImg() : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("TestUI","testUI");
         if(<PERSON><PERSON><PERSON>(img0))
         {
            addChildAt(img0,0);
            this.closeBtn = img0["closeBtn"];
            this.labelTag = img0["labelTag"];
            this.closeBtn.addEventListener(MouseEvent.CLICK,this.hide);
            addChild(this.labelBox);
            NormalUICtrl.setTag(this.labelBox,this.labelTag);
            this.labelBox.arg.init(5,1,-6,0);
            this.labelBox.inData("longLabelBtn",this.labelArr,["4399存档","内部存档","作弊大全"]);
            this.labelBox.setChoose("xml");
            this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
            this.saveBox.setCon(this);
            this.saveBox.initImg();
            this.save4399.setCon(this);
            this.save4399.initImg();
            this.cheatingBox.setCon(this);
            this.cheatingBox.initImg();
            this.haveDataB = true;
            this.setCon(Gaming.gameSprite.coverUI);
         }
         else
         {
            // 如果没有资源文件，创建简单的界面
            this.createSimpleUI();
         }
      }

      private function createSimpleUI() : void
      {
         // 创建简单的背景
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x000000, 0.8);
         bg.graphics.drawRect(0, 0, 800, 600);
         bg.graphics.endFill();
         addChildAt(bg, 0);

         // 创建关闭按钮
         var closeBtn:Sprite = new Sprite();
         closeBtn.graphics.beginFill(0xFF0000);
         closeBtn.graphics.drawRect(750, 10, 40, 30);
         closeBtn.graphics.endFill();
         closeBtn.addEventListener(MouseEvent.CLICK, this.hide);
         addChild(closeBtn);

         // 初始化子界面
         this.saveBox.setCon(this);
         this.save4399.setCon(this);
         this.cheatingBox.setCon(this);

         // 设置标签位置
         addChild(this.labelBox);
         this.labelBox.x = 50;
         this.labelBox.y = 50;
         this.labelBox.arg.init(5,1,-6,0);
         this.labelBox.inData("longLabelBtn",this.labelArr,["4399存档","内部存档","作弊大全"]);
         this.labelBox.setChoose("save4399");
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);

         this.haveDataB = true;
         this.setCon(Gaming.gameSprite.coverUI);
      }

      public function show(e:MouseEvent = null) : void
      {
         if(this.haveDataB)
         {
            this.visible = true;
            inCon();
            this.showBox(this.labelBox.nowLabel);
         }
      }
      
      private function hide(e:MouseEvent = null) : void
      {
         this.visible = false;
         outCon();
      }
      
      public function showAndHide() : void
      {
         if(this.visible)
         {
            this.hide();
         }
         else
         {
            this.show();
         }
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showBox(e.label);
      }
      
      public function showBox(label0:String) : void
      {
         var now0:NormalUI = null;
         var l0:String = null;
         var ui0:NormalUI = null;
         if(this.haveDataB)
         {
            if(label0 == "")
            {
               label0 = this.labelArr[0];
            }
            this.labelBox.setChoose(label0);
            now0 = this[label0];
            for each(l0 in this.labelArr)
            {
               ui0 = this[l0];
               if(now0 != ui0)
               {
                  ui0.hide();
               }
               else
               {
                  ui0.show();
               }
            }
         }
      }
   }
}


package UI.test
{
   import UI.base.NormalUI;
   import com.common.text.TextWay;
   import com.sounto.cf.ObjectToXml;
   import dataAll._player.PlayerCtrl;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.system.System;
   import flash.text.TextField;
   
   public class Save4399Box extends NormalUI
   {
      
      public var outOutBtn:SimpleButton;
      
      private var inputBeforeBtn:SimpleButton;
      
      private var inputAffterBtn:SimpleButton;
      
      private var addBtn:SimpleButton;
      
      private var thingsInSaveBtn:SimpleButton;
      
      private var converToGiftBtn:SimpleButton;
      
      private var beforeTxt:TextField;
      
      private var affterTxt:TextField;
      
      private var addTxt:TextField;
      
      public function Save4399Box()
      {
         super();
      }
      
      public function initImg() : void
      {
         var img0:Sprite = Gaming.swfLoaderManager.getResource("TestUI","saveTest4399");
         if(<PERSON><PERSON><PERSON>(img0)) {
            this.setImg(img0);
         } else {
            this.createSimple4399UI();
         }
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["converToGiftBtn","addBtn","outOutBtn","beforeTxt","affterTxt","addTxt","inputBeforeBtn","inputAffterBtn","thingsInSaveBtn"];
         super.setImg(img0);
         this.beforeTxt.text = "";
         this.affterTxt.text = "";
         this.addTxt.text = "";
         if(Boolean(this.outOutBtn))
         {
            this.outOutBtn.addEventListener(MouseEvent.CLICK,this.outClick);
         }
         this.inputAffterBtn.addEventListener(MouseEvent.CLICK,this.inputClick);
         this.inputBeforeBtn.addEventListener(MouseEvent.CLICK,this.inputClick);
         this.addBtn.addEventListener(MouseEvent.CLICK,this.addClick);
         if(Boolean(this.thingsInSaveBtn))
         {
            this.thingsInSaveBtn.addEventListener(MouseEvent.CLICK,this.thingsInSaveClick);
         }
         if(Boolean(this.converToGiftBtn))
         {
            this.converToGiftBtn.addEventListener(MouseEvent.CLICK,this.converToGiftClick);
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function outClick(e:MouseEvent) : void
      {
         var pg0:PlayerCtrl = Gaming.PG;
         var str0:String = ObjectToXml.decode4399Two(pg0.save.getCopyObj()).toString();
         System.setClipboard(str0);
         Gaming.uiGroup.alertBox.showSuccess("输出当前存档的4399代码到剪贴板！");
      }
      
      private function inputClick(e:MouseEvent) : void
      {
         var errorStr0:String = "";
         var saveStr0:String = "";
         if(e.target == this.inputBeforeBtn)
         {
            saveStr0 = this.beforeTxt.text;
         }
         else
         {
            saveStr0 = this.affterTxt.text;
         }
         this.inputStr(saveStr0);
      }
      
      private function inputStr(saveStr0:String) : void
      {
         var obj0:Object = {};
         obj0 = ObjectToXml.encode4399(XML(saveStr0));
         Gaming.uiGroup.loginUI.outLoginEvent();
         Gaming.PG.loginData.newSave(0);
         Gaming.uiGroup.loginUI.yes_read(obj0);
      }
      
      public function inputBeforeTxt() : void
      {
         this.inputStr(this.beforeTxt.text);
      }
      
      public function getBeforeTextStr() : String
      {
         if(Boolean(this.beforeTxt))
         {
            return this.beforeTxt.text;
         }
         return "";
      }
      
      private function addClick(e:MouseEvent) : void
      {
         var errorStr0:String = "";
         var gg0:GiftAddDefineGroup = this.getThingsGiftAddDefineGroup();
         if(!gg0)
         {
            return;
         }
         var saveStr0:String = this.beforeTxt.text;
         var obj0:Object = {};
         obj0 = ObjectToXml.encode4399(XML(saveStr0));
         var pg0:PlayerCtrl = new PlayerCtrl();
         pg0.readSave(obj0,"me",Gaming.api.save.s4399.getLogObjNull());
         var successB0:Boolean = GiftAddit.addDefineGroupByOtherPlayerData(gg0,pg0.da);
         if(successB0)
         {
            this.affterTxt.text = ObjectToXml.decode4399(pg0.save.getCopyObj()).toString();
            System.setClipboard(this.affterTxt.text);
            Gaming.uiGroup.alertBox.showSuccess("成功添加物品，并且已复制存档代码到剪贴板！");
         }
      }
      
      private function thingsInSaveClick(e:MouseEvent) : void
      {
         var gg0:GiftAddDefineGroup = this.getThingsGiftAddDefineGroup();
         if(!gg0)
         {
            return;
         }
         GiftAddit.addAndAutoBagSpacePan(gg0);
      }
      
      private function converToGiftClick(e:MouseEvent) : void
      {
         var gg0:GiftAddDefineGroup = this.getThingsGiftAddDefineGroup();
         this.affterTxt.text = gg0.getXMLString();
      }
      
      private function getThingsGiftAddDefineGroup() : GiftAddDefineGroup
      {
         var n:* = undefined;
         var str0:String = null;
         var errorStr0:String = "";
         var gg0:GiftAddDefineGroup = new GiftAddDefineGroup();
         var addStr0:String = this.addTxt.text;
         var strArr0:Array = addStr0.split(addStr0.indexOf("\r") > 0 ? "\r" : "\n");
         for(n in strArr0)
         {
            str0 = TextWay.toHanSpace(strArr0[n]);
            errorStr0 = gg0.addGiftByCnStr(str0);
            if(errorStr0 != "")
            {
               break;
            }
         }
         if(errorStr0 != "")
         {
            Gaming.uiGroup.alertBox.showError("格式不对：\n" + errorStr0);
            return null;
         }
         return gg0;
      }

      private function createSimple4399UI() : void
      {
         // 创建简单的4399存档界面
         var bg:Sprite = new Sprite();
         bg.graphics.beginFill(0x004400, 0.9);
         bg.graphics.drawRect(0, 0, 600, 400);
         bg.graphics.endFill();
         addChild(bg);

         // 创建输出按钮
         this.outOutBtn = new SimpleButton();
         var btnBg:Sprite = new Sprite();
         btnBg.graphics.beginFill(0x00FF00);
         btnBg.graphics.drawRect(0, 0, 100, 30);
         btnBg.graphics.endFill();
         this.outOutBtn.upState = btnBg;
         this.outOutBtn.overState = btnBg;
         this.outOutBtn.downState = btnBg;
         this.outOutBtn.hitTestState = btnBg;
         this.outOutBtn.x = 10;
         this.outOutBtn.y = 10;
         this.outOutBtn.addEventListener(MouseEvent.CLICK, this.outClick);
         addChild(this.outOutBtn);

         // 创建文本区域
         this.beforeTxt = new TextField();
         this.beforeTxt.x = 10;
         this.beforeTxt.y = 50;
         this.beforeTxt.width = 280;
         this.beforeTxt.height = 340;
         this.beforeTxt.multiline = true;
         this.beforeTxt.wordWrap = true;
         this.beforeTxt.border = true;
         this.beforeTxt.borderColor = 0x666666;
         this.beforeTxt.background = true;
         this.beforeTxt.backgroundColor = 0x002200;
         this.beforeTxt.textColor = 0xFFFFFF;
         this.beforeTxt.text = "4399存档输入区域";
         addChild(this.beforeTxt);

         this.affterTxt = new TextField();
         this.affterTxt.x = 310;
         this.affterTxt.y = 50;
         this.affterTxt.width = 280;
         this.affterTxt.height = 340;
         this.affterTxt.multiline = true;
         this.affterTxt.wordWrap = true;
         this.affterTxt.border = true;
         this.affterTxt.borderColor = 0x666666;
         this.affterTxt.background = true;
         this.affterTxt.backgroundColor = 0x002200;
         this.affterTxt.textColor = 0xFFFFFF;
         this.affterTxt.text = "4399存档输出区域";
         addChild(this.affterTxt);
      }
   }
}


package
{
   import flash.display.MovieClip;
   import flash.display.StageScaleMode;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   [SWF(width="950",height="600",backgroundColor="#000000",frameRate="30")]
   public class TestGaming extends MovieClip
   {
      private var testUI:*;
      private var infoText:TextField;
      
      public function TestGaming()
      {
         super();
         if(stage) {
            init();
         } else {
            addEventListener(Event.ADDED_TO_STAGE, init);
         }
      }
      
      private function init(e:Event = null):void
      {
         if(e) {
            removeEventListener(Event.ADDED_TO_STAGE, init);
         }
         
         stage.scaleMode = StageScaleMode.NO_SCALE;
         stage.showDefaultContextMenu = false;
         
         // 创建信息文本
         createInfoText();
         
         // 尝试加载测试界面
         loadTestUI();
      }
      
      private function createInfoText():void
      {
         infoText = new TextField();
         infoText.width = 950;
         infoText.height = 600;
         infoText.multiline = true;
         infoText.wordWrap = true;
         
         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = 14;
         format.color = 0xFFFFFF;
         infoText.defaultTextFormat = format;
         
         infoText.text = "爆枪突击 - 测试模式\n\n";
         infoText.appendText("当前状态: 正在初始化...\n");
         infoText.appendText("版本: 34.64 (修改版)\n\n");
         infoText.appendText("说明:\n");
         infoText.appendText("- 这是一个简化的测试版本\n");
         infoText.appendText("- 缺少游戏资源文件(SWF)\n");
         infoText.appendText("- 只能使用基本的测试功能\n\n");
         infoText.appendText("点击任意位置尝试加载测试界面...");
         
         addChild(infoText);
         
         stage.addEventListener(MouseEvent.CLICK, onStageClick);
      }
      
      private function onStageClick(e:MouseEvent):void
      {
         infoText.appendText("\n\n正在尝试加载测试界面...");
         
         try {
            // 尝试创建测试界面
            if(Gaming && Gaming.uiGroup && Gaming.uiGroup.testUI) {
               Gaming.uiGroup.testUI.show();
               infoText.appendText("\n测试界面加载成功!");
            } else {
               infoText.appendText("\n错误: Gaming对象未初始化");
               infoText.appendText("\n请确保已正确编译所有ActionScript文件");
            }
         } catch(error:Error) {
            infoText.appendText("\n错误: " + error.message);
            infoText.appendText("\n建议: 需要完整的游戏资源文件才能正常运行");
         }
      }
      
      private function loadTestUI():void
      {
         infoText.appendText("\n检查Gaming类...");
         
         // 检查Gaming类是否可用
         try {
            if(Gaming) {
               infoText.appendText("\nGaming类已加载");
               infoText.appendText("\n尝试初始化基本组件...");
               
               // 尝试初始化
               Gaming.ME = new Gaming();
               infoText.appendText("\nGaming实例已创建");
            }
         } catch(error:Error) {
            infoText.appendText("\n无法加载Gaming类: " + error.message);
         }
      }
   }
}
